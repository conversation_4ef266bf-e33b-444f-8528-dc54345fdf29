version: '3.8'

services:
  # FastAPI Application
  multitenant-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: multitenant-backend
    ports:
      - "8207:8000"  # External port 8207 maps to internal port 8000
    environment:
      - MONGO_URI=${MONGO_URI}
      - DATABASE_NAME=${DATABASE_NAME:-multi_tenant_admin}
      - PYTHONPATH=/app
    env_file:
      - .env
    volumes:
      - .:/app
      - /app/__pycache__
    networks:
      - multitenant-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s



networks:
  multitenant-network:
    driver: bridge
