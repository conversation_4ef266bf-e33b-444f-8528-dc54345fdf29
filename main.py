from fastapi import <PERSON><PERSON><PERSON>
from fastapi.security import <PERSON>A<PERSON>2P<PERSON><PERSON><PERSON>earer
from contextlib import asynccontextmanager
from app.routers import users,tenants
from app.tenant.routes.create_tenant import tenant_router
from app.core.config_loader import load_config_from_db, get_allowed_origins
from fastapi.middleware.cors import CORSMiddleware

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Load configuration from database on startup
    await load_config_from_db()
    yield

app = FastAPI(
    title="Multi-Tenant Admin System",
    description="""
    Admin system for managing tenants and users across different products.

    ## Authentication
    Use the **Authorize** button below to login:
    - Username: admin
    - Password: admin

    ## Available Endpoints
    - **POST /auth/login**: Login to get access token
    - **POST /create_user**: Create new users (Admin only)
    - **POST /tenants/create**: Create new tenants (Admin only)
    - **GET /tenants/list**: List all tenants (Admin only)
    """,
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
    origins=["*"]
)

# CORS middleware using database configuration
# @app.middleware("http")
# async def cors_middleware(request, call_next):
#     response = await call_next(request)

#     # Get allowed origins from database
#     allow_origins = await get_allowed_origins()
#     # allow_origins=["*"]
#     origin = request.headers.get("origin")

#     if origin in allow_origins:
#         response.headers["Access-Control-Allow-Origin"] = origin
#         response.headers["Access-Control-Allow-Credentials"] = "true"
#         response.headers["Access-Control-Allow-Methods"] = "*"
#         response.headers["Access-Control-Allow-Headers"] = "*"

#     return response

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 👈 Allow all origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint for Docker health checks"""
    return {"status": "healthy", "message": "Multi-Tenant Admin System is running"}

app.include_router(users.router)
app.include_router(tenant_router)
app.include_router(tenants.router)
