from pymongo import AsyncMongoClient
from dotenv import load_dotenv
from bson import ObjectId
import os
from fastapi import HTTPException

load_dotenv()

# Global client instance
_client = None

async def get_async_client():
    """Get the async MongoDB client"""
    global _client
    if _client is None:
        _client = AsyncMongoClient(os.getenv("MONGO_URI"))
    return _client

async def get_admin_db():
    """Get admin database with async client"""
    try:
        client = await get_async_client()
        return client["multi_tenant_admin"]
    except Exception as e:
        raise Exception("Admin database not found")

async def get_eko_admin_db():
    """Get admin database with async client"""
    try:
        client =await get_async_client()
        return client["eko_admin"]
    except Exception as e:
        raise Exception("Admin database not found")


async def get_async_db_from_tenant_id(tenant_id: str):
    """Get tenant database by tenant ID"""
    try:
        admin_db = await get_eko_admin_db()
        print("admin_db tenant_id",tenant_id)
        tenant = await admin_db.tenants.find_one({"_id": ObjectId(tenant_id)})
        if not tenant:
            raise Exception("Tenant not found")

        tenant_database_name = tenant["database_name"]
        client =await get_async_client()
        return client[tenant_database_name]
    except Exception as e:
        raise Exception("Tenant database not found")

async def get_tenant_id_from_slug(slug: str):
    """Get tenant ID from slug"""
    try:
        admin_db = await get_eko_admin_db()
        tenant = await admin_db.tenants.find_one({"slug":slug},{"_id": 1, "name": 1, "label": 1,"database_name": 1})
        if not tenant:
            raise HTTPException(status_code=404, detail="Tenant with this slug not found")
        return str(tenant['_id'])
    except Exception as e:
        raise HTTPException(status_code=404, detail="Tenant with this slug not found")