from datetime import datetime, timed<PERSON><PERSON>
from pydantic import BaseModel
from typing import Optional, Literal
from bson import ObjectId
import aiohttp

from app.core.security import create_invitation_token
from app.utils import convert_objectid_to_str
from app.core.database import get_async_client
from app.core.security import create_access_token
class TenantExistsError(Exception):
    """Custom exception for when tenant already exists"""
    def __init__(self, field: str, value: str, message: str = None):
        self.field = field
        self.value = value
        self.message = message or f"Tenant with {field} '{value}' already exists"
        super().__init__(self.message)

class UserRegistration(BaseModel):
    """Model for user registration data"""
    username: str
    password: str
    role: Optional[Literal["admin", "supervisor", "agent"]] = "admin"


class Tenant:
    """
    Tenant management class for creating and configuring new tenants.

    Handles:
    - Tenant registration in admin database
    - Client database creation and setup
    - Default data insertion
    - Environment configuration
    """
    def __init__(self, name: str, slug: str, username: str, requirements: dict, product_db, current_user, db_client=None):
        self.name = name
        self.slug = slug
        self.username = username
        self.product_db = product_db
        self.requirements = requirements
        self.current_user = current_user
        self.db_client = db_client or None
        self.token=None

    def _get_required_list(self, requirement_type: str):
        """Get required list from requirements configuration"""
        return self.requirements.get(requirement_type, [])

    async def _get_db_client(self):
        """Get database client - use provided client or create new one"""
        if self.db_client:
            return self.db_client
        return await get_async_client()

    def _get_product_db(self):
        """Get product database connection"""
        return self.product_db


    def _prepare_tenant(self, db_prefix: str = "eko"):
        """Prepare tenant data with configurable database prefix"""
        name = self.name.strip()
        slug = self.slug.replace(" ", "_").lower()
        database_name = f"{db_prefix}_{slug}_db"
        label = self.name.title()

        return {
            "name": name,
            "slug": slug,
            "database_name": database_name,
            "label": label,
            "topic_generation": False,
            "created_at": datetime.now(),
            "created_by": self.current_user.user.username if self.current_user else "system"
        }
    
    async def _register_tenant(self, db_prefix: str = "eko"):
        """Register tenant in product database with proper validation"""
        tenant_data = self._prepare_tenant(db_prefix)
        product_db = self._get_product_db()

        # Ensure tenants collection exists
        collections = await product_db.list_collection_names()
        if "tenants" in collections:
            collection = product_db["tenants"]
        else:
            collection = await product_db.create_collection("tenants")

        # Check if tenant with same slug already exists
        existing_slug = await collection.find_one({"slug": self.slug})
        if existing_slug:
            raise TenantExistsError("slug", self.slug)

        # Check if tenant with same name already exists
        existing_name = await collection.find_one({"name": self.name})
        if existing_name:
            raise TenantExistsError("name", self.name)

        # Insert tenant data
        result = await collection.insert_one(tenant_data)
        if not result.acknowledged:
            raise Exception("Tenant registration failed")

        return str(result.inserted_id)
    
    async def _get_tenant_id(self):
        """Get tenant id from slug and return as string"""
        product_db = self._get_product_db()
        result = await product_db.tenants.find_one({"slug": self.slug})
        if result:
            return str(result["_id"])
        else:
            raise Exception(f"Tenant with slug '{self.slug}' not found")

    async def _prepare_client_database(self, db_prefix: str = "eko"):
        """Create database and collections for client tenant"""
        tenant_data = self._prepare_tenant(db_prefix)
        database_name = tenant_data["database_name"]
        client = await self._get_db_client()

        # Check if database already exists
        database_names = await client.list_database_names()
        if database_name in database_names:
            raise Exception(f"Database '{database_name}' already exists")

        client_db = client[database_name]

        # Get required collections from requirements or use defaults
        required_collections = self._get_required_list("required_collections")
        if not required_collections:
            required_collections = ["users", "tools", "settings", "prompt", "invitations"]

        # Create collections
        for collection_name in required_collections:
            collection_names = await client_db.list_collection_names()
            if collection_name not in collection_names:
                await client_db.create_collection(collection_name)

        return database_name
        
    async def _get_client_db(self, db_prefix: str = "eko"):
        """Get client database connection"""
        tenant_data = self._prepare_tenant(db_prefix)
        database_name = tenant_data["database_name"]
        client = await self._get_db_client()

        # Verify database exists
        database_names = await client.list_database_names()
        if database_name not in database_names:
            raise Exception(f"Database '{database_name}' does not exist")

        return client[database_name]
    
    async def _prepare_env(self, service_prefix: str = "eko"):
        """Prepare environment configuration for tenant"""
        product_db = self._get_product_db()
        env_schema = await product_db["settings"].find_one({"name": "env"})
        if not env_schema:
            raise Exception("Environment schema not found in product database")

        # Convert ObjectIds to strings for JSON serialization
        env_schema = convert_objectid_to_str(env_schema)

        # Update tenant-specific configurations
        if "qdrant_config" in env_schema:
            env_schema["qdrant_config"]["coll_name"] = f"{self.slug}_sentence_context"
            env_schema["qdrant_config"]["sentence_collection"] = f"{self.slug}_sentence_context"
            env_schema["qdrant_config"]["page_collection"] = f"{self.slug}_test_page_info"
            env_schema["qdrant_config"]["sentence_split_collection"] = f"{self.slug}_sentence_split"

        if "minio_config" in env_schema:
            env_schema["minio_config"]["bucket_name"] = f"{service_prefix}.{self.slug}"

        return env_schema
    
    async def _insert_default_data(self, db_prefix: str = "eko", service_prefix: str = "eko"):
        """Insert default data into client database"""
        product_db = self._get_product_db()

        # Get default admin user (configurable username)
        default_admin_username = self._get_required_list("default_admin_username")
        if not default_admin_username:
            default_admin_username = ["superadmin"]

        default_user = None
        for username in default_admin_username:
            default_user = await product_db["users"].find_one({"username": username})
            if default_user:
                break

        if not default_user:
            raise Exception(f"Default admin user not found. Tried usernames: {default_admin_username}")

        client_db = await self._get_client_db(db_prefix)
        result = await client_db.users.insert_one(default_user)
        if not result.acknowledged:
            raise Exception("Default user insertion failed")

        # Insert default prompts
        required_prompts = self._get_required_list("required_prompt")
        if required_prompts:
            cursor = product_db["prompt"].find({"name": {"$in": required_prompts}})
            default_prompts = await cursor.to_list()
            if default_prompts:
                # Convert ObjectIds to strings
                default_prompts = [convert_objectid_to_str(prompt) for prompt in default_prompts]
                result = await client_db.prompt.insert_many(default_prompts)
                if not result.acknowledged:
                    raise Exception("Default prompt insertion failed")

        # Insert default tools
        required_tools = self._get_required_list("required_tools")
        if required_tools:
            cursor = product_db["tools"].find({"name": {"$in": required_tools}})
            default_tools = await cursor.to_list()
            if default_tools:
                # Convert ObjectIds to strings
                default_tools = [convert_objectid_to_str(tool) for tool in default_tools]
                result = await client_db.tools.insert_many(default_tools)
                if not result.acknowledged:
                    raise Exception("Default tools insertion failed")

        # Insert default settings
        required_settings = self._get_required_list("required_settings")
        if required_settings:
            cursor = product_db["settings"].find({"name": {"$in": required_settings}})
            default_settings = await cursor.to_list(length=100)
            if default_settings:
                # Convert ObjectIds to strings
                default_settings = [convert_objectid_to_str(setting) for setting in default_settings]
                result = await client_db.settings.insert_many(default_settings)
                if not result.acknowledged:
                    raise Exception("Default settings insertion failed")

        # Insert environment configuration
        env_schema = await self._prepare_env(service_prefix)
        result = await client_db.settings.insert_one(env_schema)
        if not result.acknowledged:
            raise Exception("Environment settings insertion failed")

        # Create invitation token and save invitation record
        client_tenant_id = await self._get_tenant_id()

        # Find the admin user in client database
        admin_user = await client_db.users.find_one({"role": "admin"})
        if not admin_user:
            raise Exception("Admin user not found in client database")

        # Get invitation configuration
        invitation_config = self._get_required_list("invitation_config")
        if not invitation_config:
            invitation_config = {
                "expires_days": 365,
                "role": "admin",
                "base_url": "https://eko.nextai.asia"
            }
        else:
            invitation_config = invitation_config[0] if isinstance(invitation_config, list) else invitation_config

        expires_days = invitation_config.get("expires_days", 365)
        invitation_role = invitation_config.get("role", "admin")
        base_url = invitation_config.get("base_url", "https://eko.nextai.asia")

        # Create invitation token
        invite_token = await create_invitation_token(
            username=self.username,
            role=invitation_role,
            invited_by=str(admin_user["_id"]),
            tenant_id=client_tenant_id,
            expires_delta=timedelta(days=expires_days)
        )

        # Save invitation record in invitations collection
        invitation_record = {
            "username": self.username,
            "slug": self.slug,
            "token": invite_token,
            "role": invitation_role,
            "invited_by": str(admin_user["_id"]),
            "expires_at": datetime.now() + timedelta(days=expires_days),
            "used": False,
            "permissions": {"all": True},
            "created_by": self.current_user.user.username if self.current_user else "system"
        }

        # Insert invitation record
        result = await client_db.invitations.insert_one(invitation_record)
        if not result.acknowledged:
            raise Exception("Invitation record insertion failed")

        # Generate invitation link
        invitation_link = f"{base_url}/invitation?token={invite_token}&username={self.username}&role={invitation_role}"

        return invitation_link


    async def call_dummy_setup(self):
        """Call dummy setup function"""

        # use aiohttp to call dummy route

        # Get the superadmin user information
        product_db = self._get_product_db()
        superadmin_user = await product_db["users"].find_one({"username": "superadmin"})
        if not superadmin_user:
            raise Exception("Superadmin user not found")

        # Get the newly created tenant ID
        tenant_id = await self._get_tenant_id()

        access_token = await create_access_token(
            data={
                "sub": superadmin_user["username"],
                "role": superadmin_user["role"],
                "user_id": str(superadmin_user["_id"]),
                "tenant_id": tenant_id
            },
            expires_delta=timedelta(minutes=120)
        )
        # Use the access token in the HTTP request
        headers = {"Authorization": f"Bearer {access_token}"}
        async with aiohttp.ClientSession() as session:
            async with session.get("http://eko-api2.nextai.asia/tenants/dummy-data", headers=headers) as response:
                if response.status == 200:
                    print("Dummy setup successful")
                    return await response.json()
                else:
                    raise Exception(f"Dummy setup failed with status {response.status}")