from fastapi import APIRouter, Depends, HTTPException,Depends
from app.core.database import get_async_db_from_tenant_id,get_async_client
from app.utils import convert_objectid_to_str

from app.core.permissions import require_admin
from app.models.user import UserTenantDB
from bson import ObjectId

router = APIRouter(tags=["metrics"],prefix="/metrics")


@router.get("/{tenant_id}", )
async def get_tenant_by_id(
    tenant_id: str,
    current_user: UserTenantDB =Depends(require_admin())
):
    """
    Get specific tenant details by ID.
    Only admins can view tenant details.
    """
    try:

        # Validate ObjectId format
        try:
            object_id = ObjectId(tenant_id)
        except Exception:
            raise HTTPException(
                status_code=400,
                detail="Invalid tenant ID format"
            )

        tenant_data = await current_user.db.tenants.find_one(
            {"_id": object_id},
            {
                "name": 1,
                "slug": 1,
                "database_name": 1,
                "_id": 1
            }
        )


        tenant_db= await get_async_client()
        
        tenants= await tenant_db[t].tenants.find({}).to_list(length=None)
        return convert_objectid_to_str(tenants)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching tenant: {str(e)}"
        )
